#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查四年数据合并结果
"""

import pandas as pd
import os

def check_data():
    """检查四年合并数据"""
    
    file_path = "finebi_data_3years/高考分数线合并数据_2021-2024.xlsx"
    
    if os.path.exists(file_path):
        df = pd.read_excel(file_path)
        
        print("=== 四年数据检查结果 ===")
        print(f"数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        
        # 年份分布
        year_counts = df['年份'].value_counts().sort_index()
        print(f"\n年份分布:")
        for year, count in year_counts.items():
            print(f"  {year}年: {count}条记录")
        
        # 省份统计
        print(f"\n省份统计:")
        print(f"总省份数: {df['地区'].nunique()}")
        
        # 各年份省份数量
        for year in sorted(df['年份'].unique()):
            year_data = df[df['年份'] == year]
            print(f"  {year}年: {year_data['地区'].nunique()}个省份")
        
        # 考生类别和录取批次
        print(f"\n考生类别: {df['考生类别'].unique()}")
        print(f"录取批次: {df['录取批次'].unique()}")
        
        # 分数线统计
        print(f"\n分数线统计:")
        print(f"最高分: {df['录取分数线'].max()}")
        print(f"最低分: {df['录取分数线'].min()}")
        print(f"平均分: {df['录取分数线'].mean():.1f}")
        
        # 显示前几行
        print(f"\n前5行数据:")
        print(df.head())
        
        return True
    else:
        print(f"文件不存在: {file_path}")
        return False

if __name__ == "__main__":
    check_data()
