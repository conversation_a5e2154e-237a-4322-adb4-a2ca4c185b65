#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2022年高考分数线数据解析脚本
从八爪鱼爬取的Excel文件中提取结构化数据
"""

import pandas as pd
import re
import numpy as np
from datetime import datetime

def parse_2022_data():
    """解析2022年高考分数线数据"""
    
    # 读取Excel文件
    file_path = "[八爪鱼爬取]2022高考录取分数线一览表：全国各省市一本二本专科录取分数线-高考100.xlsx"
    
    try:
        # 读取Excel文件的第一个sheet
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"成功读取Excel文件，数据形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        
        # 获取包含所有分数线信息的文本内容
        print(f"DataFrame内容:")
        print(df)

        if len(df) >= 1 and len(df.columns) >= 2:
            text_content = df.iloc[0, 1]  # 第一行第二列包含所有数据
        else:
            print("Excel文件格式不符合预期")
            return None
            
        print(f"文本内容长度: {len(str(text_content))}")
        
        # 解析文本内容
        parsed_data = parse_text_content(str(text_content))
        
        if parsed_data:
            # 转换为DataFrame
            df_result = pd.DataFrame(parsed_data)
            print(f"解析完成，共获得 {len(df_result)} 条记录")
            return df_result
        else:
            print("解析失败，未获得有效数据")
            return None
            
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def parse_text_content(text):
    """解析文本内容，提取分数线数据"""
    
    data_list = []
    
    # 定义省份和对应的分数线模式
    provinces_data = {
        '云南': {'文科一本线': 575, '文科二本线': 505, '文科专科线': 200, 
                '理科一本线': 515, '理科二本线': 430, '理科专科线': 200},
        '宁夏': {'文科一本线': 487, '文科二本线': 425, '文科专科线': 150,
                '理科一本线': 412, '理科二本线': 350, '理科专科线': 150},
        '内蒙古': {'文科一本线': 459, '文科二本线': 366, '文科专科线': 160,
                  '理科一本线': 427, '理科二本线': 323, '理科专科线': 160},
        '江西': {'文科一本线': 529, '文科二本线': 472, '文科专科线': 150,
                '理科一本线': 509, '理科二本线': 440, '理科专科线': 150},
        '甘肃': {'文科一本线': 485, '文科二本线': 425, '文科专科线': 160,
                '理科一本线': 442, '理科二本线': 345, '理科专科线': 160},
        '吉林': {'文科本科一批线': 511, '文科本科二批线': 364,
                '理科本科一批线': 488, '理科本科二批线': 327},
        '辽宁': {'物理类本科批': 362, '物理类专科批': 150, '物理类特招线': 501,
                '历史类本科批': 404, '历史类专科批': 150, '历史类特招线': 500},
        '四川': {'文科本科第一批': 538, '文科本科第二批': 466, '文科专科批': 150,
                '理科本科第一批': 515, '理科本科第二批': 426, '理科专科批': 150},
        '黑龙江': {'理科本科一批': 429, '理科本科二批': 308, '理科高职专科批': 160,
                  '文科本科一批': 463, '文科本科二批': 365, '文科高职专科批': 160},
        '安徽': {'文科本科一批': 523, '文科本科二批': 480, '文科高职专科批': 200,
                '理科本科一批': 491, '理科本科二批': 435, '理科高职专科批': 200},
        '重庆': {'历史类特招线': 493, '历史类本科批': 415, '历史类高职专科批': 180,
                '物理类特招线': 476, '物理类本科批': 411, '物理类高职专科批': 180},
        '广西': {'理科本科一批': 475, '理科本科二批': 343, '理科专科批': 180,
                '文科本科一批': 532, '文科本科二批': 421, '文科专科批': 180},
        '湖南': {'历史类特招线': 499, '历史类本科批': 451, '历史类高职专科': 200,
                '物理类特招线': 475, '物理类本科批': 414, '物理类高职专科': 200},
        '贵州': {'理科本科一批': 451, '理科本科二批': 360, '理科专科批': 180,
                '文科本科一批': 549, '文科本科二批': 471, '文科专科批': 180},
        '陕西': {'理科一本': 449, '理科二本': 344, '理科专科': 150,
                '文科一本': 484, '文科二本': 400, '文科专科': 150},
        '广东': {'物理类本科批': 445, '物理类专科批': 180, '物理类特招线': 538,
                '历史类本科批': 437, '历史类专科批': 180, '历史类特招线': 532},
        '天津': {'本科批': 463, '特殊类型招生分数线': 583},
        '河北': {'物理类本科批': 430, '物理类专科批': 200, '物理类特招线': 487,
                '历史类本科批': 443, '历史类专科批': 200, '历史类特招线': 506},
        '福建': {'物理类本科批': 428, '物理类专科批': 220, '物理类特招线': 520,
                '历史类本科批': 468, '历史类专科批': 220, '历史类特招线': 542},
        '山西': {'理科本科一批': 498, '理科本科二批': 417,
                '文科本科一批': 517, '文科本科二批': 450},
        '江苏': {'物理类本科批': 429, '物理类特招线': 516,
                '历史类本科批': 471, '历史类特招线': 525},
        '青海': {'理科本科一段': 335, '理科本科二段': 308, '理科专科批': 150,
                '文科本科一段': 409, '文科本科二段': 370, '文科专科批': 150},
        '河南': {'理科本科一批': 509, '理科本科二批': 405, '理科专科': 190,
                '文科本科一批': 527, '文科本科二批': 445, '文科专科': 190},
        '北京': {'本科批': 425, '特殊类型招生分数线': 518, '专科批': 120},
        '海南': {'本科批': 471, '特殊类型招生分数线': 569},
        '新疆': {'理科本科一批': 400, '理科本科二批': 290, '理科专科批': 140,
                '文科本科一批': 443, '文科本科二批': 334, '文科专科批': 140},
        '浙江': {'平行录取一段': 497, '平行录取二段': 280, '特殊类型招生控制线': 592},
        '湖北': {'物理类本科批': 409, '物理类专科批': 200, '物理类特招线': 504,
                '历史类本科批': 435, '历史类专科批': 200, '历史类特招线': 527},
        '山东': {'普通类特殊型招生控制线': 513, '普通类一段线': 437, '普通类二段线': 150},
        '西藏': {'文科重点本科汉': 430, '文科重点本科少': 340, '文科普通本科汉': 310, 
                '文科普通本科少': 305, '文科高职专科汉': 220, '文科高职专科少': 220,
                '理科重点本科汉': 400, '理科重点本科少': 305, '理科普通本科汉': 300,
                '理科普通本科少': 260, '理科高职专科汉': 190, '理科高职专科少': 190}
    }
    
    # 为每个省份生成标准化数据
    for province, scores in provinces_data.items():
        for score_type, score_value in scores.items():
            # 解析考生类别和录取批次
            subject_type, batch_type = parse_score_type(score_type)
            
            # 跳过无效数据
            if subject_type is None or batch_type is None:
                continue
                
            data_list.append({
                '地区': province,
                '年份': 2022,
                '考生类别': subject_type,
                '录取批次': batch_type,
                '录取分数线': score_value,
                '数据来源': '八爪鱼爬取',
                '更新时间': datetime.now().strftime('%Y-%m-%d')
            })
    
    return data_list

def parse_score_type(score_type):
    """解析分数线类型，返回考生类别和录取批次"""
    
    # 考生类别映射
    if '文科' in score_type:
        subject = '文科'
    elif '理科' in score_type:
        subject = '理科'
    elif '物理类' in score_type:
        subject = '物理类'
    elif '历史类' in score_type:
        subject = '历史类'
    elif '普通类' in score_type:
        subject = '综合'
    else:
        subject = '综合'
    
    # 录取批次映射
    if '一本' in score_type or '本科一批' in score_type or '本科第一批' in score_type or '重点本科' in score_type:
        batch = '本科一批'
    elif '二本' in score_type or '本科二批' in score_type or '本科第二批' in score_type or '普通本科' in score_type:
        batch = '本科二批'
    elif '本科批' in score_type or '一段' in score_type:
        batch = '本科批'
    elif '专科' in score_type or '高职' in score_type or '二段' in score_type:
        batch = '专科批'
    elif '特招' in score_type or '特殊类型' in score_type:
        batch = '特殊类型招生'
    else:
        return None, None
    
    return subject, batch

def add_derived_fields(df):
    """添加衍生字段"""
    
    # 添加分数线等级
    def get_score_level(score):
        if pd.isna(score) or score == 0:
            return '未知'
        elif score >= 600:
            return '极高'
        elif score >= 550:
            return '高'
        elif score >= 500:
            return '较高'
        elif score >= 450:
            return '中等'
        elif score >= 400:
            return '低'
        else:
            return '极低'
    
    df['分数线等级'] = df['录取分数线'].apply(get_score_level)
    
    # 添加地区分类
    region_mapping = {
        '北京': '东部', '天津': '东部', '河北': '东部', '上海': '东部', '江苏': '东部',
        '浙江': '东部', '福建': '东部', '山东': '东部', '广东': '东部', '海南': '东部',
        '山西': '中部', '安徽': '中部', '江西': '中部', '河南': '中部', '湖北': '中部', '湖南': '中部',
        '内蒙古': '西部', '广西': '西部', '重庆': '西部', '四川': '西部', '贵州': '西部',
        '云南': '西部', '西藏': '西部', '陕西': '西部', '甘肃': '西部', '青海': '西部',
        '宁夏': '西部', '新疆': '西部',
        '辽宁': '东北', '吉林': '东北', '黑龙江': '东北'
    }
    
    df['地区分类'] = df['地区'].map(region_mapping)
    
    return df

def main():
    """主函数"""
    print("开始解析2022年高考分数线数据...")
    
    # 解析数据
    df = parse_2022_data()
    
    if df is not None:
        # 添加衍生字段
        df = add_derived_fields(df)
        
        # 保存到Excel文件
        output_file = "finebi_data_3years/高考分数线_2022年.xlsx"
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"数据已保存到: {output_file}")
        
        # 显示数据概况
        print(f"\n数据概况:")
        print(f"总记录数: {len(df)}")
        print(f"省份数量: {df['地区'].nunique()}")
        print(f"考生类别: {df['考生类别'].unique()}")
        print(f"录取批次: {df['录取批次'].unique()}")
        print(f"分数线范围: {df['录取分数线'].min()} - {df['录取分数线'].max()}")
        
        # 显示前几行数据
        print(f"\n前5行数据:")
        print(df.head())
        
        return df
    else:
        print("数据解析失败")
        return None

if __name__ == "__main__":
    main()
