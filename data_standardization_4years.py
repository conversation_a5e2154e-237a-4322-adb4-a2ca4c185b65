#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四年高考分数线数据规范化脚本 (2021-2024)
作者：数据分析小组
日期：2024年
"""

import pandas as pd
import numpy as np
import re
import os
from datetime import datetime

def load_existing_data():
    """加载现有的三年数据"""
    print("=== 加载现有三年数据 ===")
    
    # 加载现有的三年合并数据
    existing_file = "finebi_data_3years/高考分数线合并数据_2021-2024.xlsx"
    
    if os.path.exists(existing_file):
        df_existing = pd.read_excel(existing_file)
        print(f"现有数据形状: {df_existing.shape}")
        print(f"现有年份: {sorted(df_existing['年份'].unique())}")
        return df_existing
    else:
        print("未找到现有数据文件，将重新加载所有年份数据")
        return None

def load_2022_data():
    """加载2022年数据"""
    print("\n=== 加载2022年数据 ===")
    
    file_2022 = "finebi_data_3years/高考分数线_2022年.xlsx"
    
    if os.path.exists(file_2022):
        df_2022 = pd.read_excel(file_2022)
        print(f"2022年数据形状: {df_2022.shape}")
        print(f"2022年省份数量: {df_2022['地区'].nunique()}")
        return df_2022
    else:
        print("未找到2022年数据文件，请先运行 parse_2022_data.py")
        return None

def load_all_years_data():
    """加载所有年份的数据"""
    print("=== 加载所有年份数据 ===")
    
    all_data = []
    
    # 加载2021年数据
    file_2021 = "finebi_data_3years/高考分数线_2021年.xlsx"
    if os.path.exists(file_2021):
        df_2021 = pd.read_excel(file_2021)
        all_data.append(df_2021)
        print(f"2021年数据: {df_2021.shape}")
    
    # 加载2022年数据
    file_2022 = "finebi_data_3years/高考分数线_2022年.xlsx"
    if os.path.exists(file_2022):
        df_2022 = pd.read_excel(file_2022)
        all_data.append(df_2022)
        print(f"2022年数据: {df_2022.shape}")
    
    # 加载2023年数据
    file_2023 = "finebi_data_3years/高考分数线_2023年.xlsx"
    if os.path.exists(file_2023):
        df_2023 = pd.read_excel(file_2023)
        all_data.append(df_2023)
        print(f"2023年数据: {df_2023.shape}")
    
    # 加载2024年数据
    file_2024 = "finebi_data_3years/高考分数线_2024年.xlsx"
    if os.path.exists(file_2024):
        df_2024 = pd.read_excel(file_2024)
        all_data.append(df_2024)
        print(f"2024年数据: {df_2024.shape}")
    
    if all_data:
        # 合并所有数据
        df_combined = pd.concat(all_data, ignore_index=True)
        print(f"合并后总数据形状: {df_combined.shape}")
        return df_combined
    else:
        print("未找到任何年份的数据文件")
        return None

def add_derived_fields(df):
    """添加衍生字段"""
    print("\n=== 添加衍生字段 ===")
    
    # 添加分数线等级
    def get_score_level(score):
        if pd.isna(score) or score == 0:
            return '未知'
        elif score >= 600:
            return '极高'
        elif score >= 550:
            return '高'
        elif score >= 500:
            return '较高'
        elif score >= 450:
            return '中等'
        elif score >= 400:
            return '低'
        else:
            return '极低'
    
    df['分数线等级'] = df['录取分数线'].apply(get_score_level)
    
    # 添加地区分类
    region_mapping = {
        '北京': '东部', '天津': '东部', '河北': '东部', '上海': '东部', '江苏': '东部',
        '浙江': '东部', '福建': '东部', '山东': '东部', '广东': '东部', '海南': '东部',
        '山西': '中部', '安徽': '中部', '江西': '中部', '河南': '中部', '湖北': '中部', '湖南': '中部',
        '内蒙古': '西部', '广西': '西部', '重庆': '西部', '四川': '西部', '贵州': '西部',
        '云南': '西部', '西藏': '西部', '陕西': '西部', '甘肃': '西部', '青海': '西部',
        '宁夏': '西部', '新疆': '西部',
        '辽宁': '东北', '吉林': '东北', '黑龙江': '东北'
    }
    
    df['地区分类'] = df['地区'].map(region_mapping)
    
    return df

def standardize_data(df):
    """标准化数据格式"""
    print("\n=== 标准化数据格式 ===")
    
    # 确保数据类型正确
    df['年份'] = pd.to_numeric(df['年份'], errors='coerce')
    df['录取分数线'] = pd.to_numeric(df['录取分数线'], errors='coerce')
    
    # 删除包含NaN的行
    df = df.dropna(subset=['年份', '录取分数线'])
    
    # 标准化字符串字段
    string_columns = ['地区', '考生类别', '录取批次', '数据来源']
    for col in string_columns:
        if col in df.columns:
            df[col] = df[col].astype(str).str.strip()
    
    # 统一录取批次名称
    batch_mapping = {
        '本科一批': '本科一批',
        '本科二批': '本科二批', 
        '本科批': '本科批',
        '专科批': '专科批',
        '特殊类型招生': '特殊类型招生',
        '特殊类型招生控制线': '特殊类型招生'
    }
    
    df['录取批次'] = df['录取批次'].map(batch_mapping).fillna(df['录取批次'])
    
    # 统一考生类别名称
    subject_mapping = {
        '文科': '文科',
        '理科': '理科',
        '物理类': '物理类',
        '历史类': '历史类',
        '综合': '综合'
    }
    
    df['考生类别'] = df['考生类别'].map(subject_mapping).fillna(df['考生类别'])
    
    return df

def create_pivot_analysis(df):
    """创建透视分析表"""
    print("\n=== 创建透视分析表 ===")
    
    # 创建年度对比透视表
    pivot_year = pd.pivot_table(
        df, 
        values='录取分数线', 
        index=['地区', '考生类别', '录取批次'], 
        columns='年份', 
        aggfunc='mean'
    ).round(0)
    
    # 添加统计信息
    pivot_year['平均分'] = pivot_year.mean(axis=1).round(0)
    pivot_year['最高分'] = pivot_year.max(axis=1)
    pivot_year['最低分'] = pivot_year.min(axis=1)
    pivot_year['分数差'] = (pivot_year.max(axis=1) - pivot_year.min(axis=1)).round(0)
    
    return pivot_year

def generate_summary_statistics(df):
    """生成汇总统计信息"""
    print("\n=== 生成汇总统计 ===")
    
    summary = {}
    
    # 基本统计
    summary['总记录数'] = len(df)
    summary['年份范围'] = f"{df['年份'].min()}-{df['年份'].max()}"
    summary['省份数量'] = df['地区'].nunique()
    summary['年份分布'] = df['年份'].value_counts().sort_index().to_dict()
    
    # 各年份省份覆盖情况
    province_coverage = {}
    for year in sorted(df['年份'].unique()):
        year_data = df[df['年份'] == year]
        province_coverage[year] = {
            '省份数量': year_data['地区'].nunique(),
            '记录数量': len(year_data),
            '省份列表': sorted(year_data['地区'].unique())
        }
    
    summary['省份覆盖情况'] = province_coverage
    
    # 分数线统计
    summary['分数线统计'] = {
        '最高分': df['录取分数线'].max(),
        '最低分': df['录取分数线'].min(),
        '平均分': round(df['录取分数线'].mean(), 1),
        '中位数': df['录取分数线'].median()
    }
    
    return summary

def main():
    """主函数"""
    print("开始四年高考分数线数据标准化处理...")
    
    # 尝试加载现有数据
    df_existing = load_existing_data()
    
    if df_existing is not None and 2022 in df_existing['年份'].values:
        print("检测到现有数据已包含2022年，使用现有数据")
        df_combined = df_existing
    else:
        print("需要重新合并所有年份数据")
        # 加载所有年份数据
        df_combined = load_all_years_data()
        
        if df_combined is None:
            print("无法加载数据，请检查数据文件是否存在")
            return None
    
    # 标准化数据
    df_combined = standardize_data(df_combined)
    
    # 添加衍生字段
    df_combined = add_derived_fields(df_combined)
    
    # 生成汇总统计
    summary = generate_summary_statistics(df_combined)
    
    # 创建透视分析表
    pivot_analysis = create_pivot_analysis(df_combined)
    
    # 保存结果
    output_dir = "finebi_data_3years"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存合并数据
    combined_file = f"{output_dir}/高考分数线合并数据_2021-2024.xlsx"
    df_combined.to_excel(combined_file, index=False, engine='openpyxl')
    print(f"四年合并数据已保存到: {combined_file}")
    
    # 保存CSV格式
    csv_file = f"{output_dir}/高考分数线合并数据_2021-2024.csv"
    df_combined.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"CSV格式数据已保存到: {csv_file}")
    
    # 保存透视分析表
    pivot_file = f"{output_dir}/高考分数线透视分析表_四年.xlsx"
    pivot_analysis.to_excel(pivot_file, engine='openpyxl')
    print(f"透视分析表已保存到: {pivot_file}")
    
    # 打印汇总统计
    print(f"\n=== 四年数据汇总统计 ===")
    print(f"总记录数: {summary['总记录数']}")
    print(f"年份范围: {summary['年份范围']}")
    print(f"省份数量: {summary['省份数量']}")
    print(f"年份分布: {summary['年份分布']}")
    print(f"分数线统计: {summary['分数线统计']}")
    
    print(f"\n各年份省份覆盖情况:")
    for year, info in summary['省份覆盖情况'].items():
        print(f"  {year}年: {info['省份数量']}个省份, {info['记录数量']}条记录")
    
    return df_combined, summary

if __name__ == "__main__":
    main()
